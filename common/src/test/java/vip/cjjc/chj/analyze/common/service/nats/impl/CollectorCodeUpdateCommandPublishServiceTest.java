package vip.cjjc.chj.analyze.common.service.nats.impl;

import io.nats.client.Message;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ActiveProfiles;
import vip.cjjc.chj.analyze.common.config.NatsConfig;
import vip.cjjc.chj.analyze.common.service.nats.PublishService;

import java.nio.charset.StandardCharsets;

import static org.assertj.core.api.Assertions.assertThat;

@EnableAutoConfiguration
@SpringBootTest(classes = {CollectorCodeUpdateCommandPublishService.class,
        NatsConfig.class,
        ConfigurableNatsSubscriber.class})
@ActiveProfiles("test")
public class CollectorCodeUpdateCommandPublishServiceTest {

    @Autowired
    private ConfigurableNatsSubscriber configurableNatsSubscriber;

    @Autowired
    @Qualifier("collectorCodeUpdateCommandPublishService")
    private PublishService collectorCodeUpdateCommandPublishService;

    @Test
    @DisplayName("Test CommandCollectorCodePublishService is injected")
    void testCollectorCodeUpdateCommandPublishServiceIsAutowired() {
        assertThat(collectorCodeUpdateCommandPublishService).isNotNull();
    }

    @Test
    @DisplayName("Test ConfigurableNatsSubscriber is injected")
    void testConfigurableNatsSubscriberIsAutowired() {
        assertThat(configurableNatsSubscriber).isNotNull();
    }

    @Test    @DisplayName("Test CommandCollectorCodePublishService is injected")
    void testPublish() {
        collectorCodeUpdateCommandPublishService.pub("test");
        //等待1秒，等待消息发送
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();}
    }


}
