# NATS服务独立运行应用

这个目录包含了一个可独立运行的Spring Boot应用程序，用于测试和演示NATS服务功能。

## 文件说明

- `NatsServiceApplication.java` - 主应用程序类，包含交互式命令行界面
- `CollectorCodeUpdateCommandPublishServiceTest.java` - 单元测试类
- `ConfigurableNatsSubscriber.java` - NATS消息订阅者

## 功能特性

### 🚀 主要功能
- ✅ **Spring Boot集成** - 完整的Spring容器管理
- ✅ **NATS发布服务** - CollectorCodeUpdateCommandPublishService
- ✅ **NATS订阅服务** - ConfigurableNatsSubscriber
- ✅ **交互式命令行** - 实时发送和接收消息
- ✅ **配置管理** - 通过application-test.yml管理配置

### 📋 可用命令
- `send <message>` - 发送自定义消息
- `test` - 发送测试消息
- `status` - 显示服务状态
- `help` - 显示帮助信息
- `quit/exit` - 退出应用程序

## 运行方法

### 方法1: 使用IDE运行
1. 在IDE中打开 `NatsServiceApplication.java`
2. 右键选择 "Run" 或 "Debug"
3. 应用启动后会显示交互式命令行界面

### 方法2: 使用Gradle运行
```bash
# 在common目录下执行
./gradlew test --tests "vip.cjjc.chj.analyze.common.service.nats.impl.NatsServiceApplication"
```

### 方法3: 编译后运行
```bash
# 编译
./gradlew build

# 运行 (需要设置classpath)
java -cp "build/classes/java/test:build/classes/java/main:..." \
  vip.cjjc.chj.analyze.common.service.nats.impl.NatsServiceApplication
```

## 配置要求

### 1. NATS服务器
确保NATS服务器正在运行，默认配置：
- URL: `nats://********:5222`
- 用户名: `zdl` (来自 `${chj.customer_tag}`)
- 密码: `123456`

### 2. 配置文件
应用使用 `application-test.yml` 配置文件，关键配置项：

```yaml
chj:
  customer_tag: zdl

nats:
  server:
    url: nats://********:5222
  auth:
    username: ${chj.customer_tag}
    password: 123456
  subjects:
    collectorcode:
      update:
        command: ${chj.customer_tag}.command.update.collectorcode
```

### 3. 环境变量 (可选)
可以通过环境变量覆盖配置：
```bash
export NATS_SERVER_URL=nats://your-server:4222
export CUSTOMER_TAG=your_tag
export NATS_PASSWORD=your_password
```

## 使用示例

### 启动应用
```
=== NATS服务应用程序已启动 ===
验证服务注入状态...
✓ ConfigurableNatsSubscriber 已成功注入
✓ CollectorCodeUpdateCommandPublishService 已成功注入

============================================================
    NATS 收集器代码更新服务
============================================================
服务状态:
  ✓ NATS连接已建立
  ✓ 发布服务已就绪
  ✓ 订阅服务已就绪

可用命令:
  send <message>  - 发送消息到NATS
  test           - 发送测试消息
  status         - 显示服务状态
  help           - 显示帮助信息
  quit/exit      - 退出应用程序
============================================================

请输入命令 (输入 'help' 查看帮助):
nats> 
```

### 发送消息
```
nats> send Hello NATS!
✓ 消息已发送: Hello NATS!
从配置的主题 'zdl.command.update.collectorcode' 收到消息: Hello NATS!

nats> test
✓ 消息已发送: 测试消息 - 1703123456789
从配置的主题 'zdl.command.update.collectorcode' 收到消息: 测试消息 - 1703123456789
```

### 查看状态
```
nats> status

服务状态:
  发布服务: 运行中
  订阅服务: 运行中
  时间: 2024-01-01T10:30:45.123
```

## 故障排除

### 连接问题
1. **检查NATS服务器状态**
   ```bash
   # 检查NATS服务器是否运行
   telnet ******** 5222
   ```

2. **验证认证信息**
   - 确认用户名和密码正确
   - 检查NATS服务器的权限配置

3. **网络连接**
   - 确认网络可达性
   - 检查防火墙设置

### 配置问题
1. **检查配置文件**
   ```bash
   # 查看实际使用的配置
   grep -A 10 "nats:" common/src/test/resources/application-test.yml
   ```

2. **验证环境变量**
   ```bash
   echo $CUSTOMER_TAG
   echo $NATS_SERVER_URL
   ```

### 日志调试
启用调试日志：
```yaml
logging:
  level:
    vip.cjjc.chj: DEBUG
    io.nats: DEBUG
```

## 扩展功能

### 添加新的订阅者
1. 创建新的订阅者类
2. 使用 `@NatsListener` 注解
3. 在 `NatsServiceApplication` 中添加组件扫描

### 添加新的发布服务
1. 继承 `AbstractPublishService`
2. 使用 `@Service` 注解
3. 在应用中注入使用

### 自定义消息处理
修改 `ConfigurableNatsSubscriber.handleOrderCreated` 方法来实现自定义的消息处理逻辑。

## 技术架构

```
NatsServiceApplication (主应用)
├── CollectorCodeUpdateCommandPublishService (发布服务)
│   └── AbstractPublishService (抽象基类)
├── ConfigurableNatsSubscriber (订阅服务)
│   └── @NatsListener (注解驱动)
├── NatsConfig (NATS连接配置)
└── NatsListenerAnnotationProcessor (注解处理器)
```

这个应用演示了如何在Spring环境中集成NATS消息系统，提供了完整的发布-订阅功能和交互式测试界面。
