package vip.cjjc.chj.analyze.common.service.nats.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import vip.cjjc.chj.analyze.common.config.NatsConfig;
import vip.cjjc.chj.analyze.common.service.nats.PublishService;
import vip.cjjc.chj.analyze.common.service.nats.annotation.NatsListenerAnnotationProcessor;

import java.util.Scanner;
import java.util.concurrent.CountDownLatch;

/**
 * NATS服务独立运行的主应用程序
 * 
 * <p>这个应用程序演示了如何使用Spring构建和运行NATS服务，包括：</p>
 * <ul>
 *   <li>CollectorCodeUpdateCommandPublishService - 发布服务</li>
 *   <li>ConfigurableNatsSubscriber - 订阅服务</li>
 *   <li>交互式命令行界面</li>
 * </ul>
 * 
 * <p><strong>使用方法：</strong></p>
 * <pre>
 * 1. 确保NATS服务器正在运行
 * 2. 配置application-test.yml中的NATS连接信息
 * 3. 运行此类的main方法
 * 4. 在命令行中输入消息进行测试
 * </pre>
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@SpringBootApplication
@ComponentScan(basePackages = {
    "vip.cjjc.chj.analyze.common.service.nats.impl",
    "vip.cjjc.chj.analyze.common.config",
    "vip.cjjc.chj.analyze.common.service.nats.annotation"
})
@Import({
    NatsConfig.class,
    NatsListenerAnnotationProcessor.class
})
public class NatsServiceApplication implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(NatsServiceApplication.class);

    @Autowired
    private ConfigurableNatsSubscriber configurableNatsSubscriber;

    @Autowired
    @Qualifier("collectorCodeUpdateCommandPublishService")
    private PublishService collectorCodeUpdateCommandPublishService;

    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置活动配置文件为test
        System.setProperty("spring.profiles.active", "test");
        
        logger.info("启动NATS服务应用程序...");
        SpringApplication.run(NatsServiceApplication.class, args);
    }

    /**
     * Spring Boot应用启动后执行的逻辑
     * 
     * @param args 命令行参数
     */
    @Override
    public void run(String... args) {
        logger.info("=== NATS服务应用程序已启动 ===");
        
        // 验证服务是否正确注入
        validateServices();
        
        // 显示应用信息
        displayApplicationInfo();
        
        // 启动交互式命令行界面
        startInteractiveMode();
    }

    /**
     * 验证Spring服务是否正确注入
     */
    private void validateServices() {
        logger.info("验证服务注入状态...");
        
        if (configurableNatsSubscriber != null) {
            logger.info("✓ ConfigurableNatsSubscriber 已成功注入");
        } else {
            logger.error("✗ ConfigurableNatsSubscriber 注入失败");
        }
        
        if (collectorCodeUpdateCommandPublishService != null) {
            logger.info("✓ CollectorCodeUpdateCommandPublishService 已成功注入");
        } else {
            logger.error("✗ CollectorCodeUpdateCommandPublishService 注入失败");
        }
    }

    /**
     * 显示应用程序信息
     */
    private void displayApplicationInfo() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("    NATS 收集器代码更新服务");
        System.out.println("=".repeat(60));
        System.out.println("服务状态:");
        System.out.println("  ✓ NATS连接已建立");
        System.out.println("  ✓ 发布服务已就绪");
        System.out.println("  ✓ 订阅服务已就绪");
        System.out.println("\n可用命令:");
        System.out.println("  send <message>  - 发送消息到NATS");
        System.out.println("  test           - 发送测试消息");
        System.out.println("  status         - 显示服务状态");
        System.out.println("  help           - 显示帮助信息");
        System.out.println("  quit/exit      - 退出应用程序");
        System.out.println("=".repeat(60));
    }

    /**
     * 启动交互式命令行模式
     */
    private void startInteractiveMode() {
        Scanner scanner = new Scanner(System.in);
        CountDownLatch latch = new CountDownLatch(1);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("接收到关闭信号，正在优雅关闭...");
            latch.countDown();
        }));
        
        System.out.println("\n请输入命令 (输入 'help' 查看帮助):");
        
        while (true) {
            System.out.print("nats> ");
            String input = scanner.nextLine().trim();
            
            if (input.isEmpty()) {
                continue;
            }
            
            if (processCommand(input)) {
                break; // 退出循环
            }
        }
        
        scanner.close();
        logger.info("应用程序已退出");
        System.exit(0);
    }

    /**
     * 处理用户输入的命令
     * 
     * @param input 用户输入
     * @return true表示应该退出应用程序，false表示继续运行
     */
    private boolean processCommand(String input) {
        String[] parts = input.split("\\s+", 2);
        String command = parts[0].toLowerCase();
        
        switch (command) {
            case "send":
                if (parts.length > 1) {
                    sendMessage(parts[1]);
                } else {
                    System.out.println("用法: send <message>");
                }
                break;
                
            case "test":
                sendTestMessage();
                break;
                
            case "status":
                showStatus();
                break;
                
            case "help":
                showHelp();
                break;
                
            case "quit":
            case "exit":
                System.out.println("正在退出...");
                return true;
                
            default:
                System.out.println("未知命令: " + command + " (输入 'help' 查看帮助)");
                break;
        }
        
        return false;
    }

    /**
     * 发送消息到NATS
     * 
     * @param message 要发送的消息
     */
    private void sendMessage(String message) {
        try {
            collectorCodeUpdateCommandPublishService.pub(message);
            System.out.println("✓ 消息已发送: " + message);
            logger.info("发送消息: {}", message);
        } catch (Exception e) {
            System.out.println("✗ 发送消息失败: " + e.getMessage());
            logger.error("发送消息失败", e);
        }
    }

    /**
     * 发送测试消息
     */
    private void sendTestMessage() {
        String testMessage = "测试消息 - " + System.currentTimeMillis();
        sendMessage(testMessage);
    }

    /**
     * 显示服务状态
     */
    private void showStatus() {
        System.out.println("\n服务状态:");
        System.out.println("  发布服务: " + (collectorCodeUpdateCommandPublishService != null ? "运行中" : "未就绪"));
        System.out.println("  订阅服务: " + (configurableNatsSubscriber != null ? "运行中" : "未就绪"));
        System.out.println("  时间: " + java.time.LocalDateTime.now());
    }

    /**
     * 显示帮助信息
     */
    private void showHelp() {
        System.out.println("\n可用命令:");
        System.out.println("  send <message>  - 发送自定义消息到NATS主题");
        System.out.println("  test           - 发送带时间戳的测试消息");
        System.out.println("  status         - 显示当前服务状态");
        System.out.println("  help           - 显示此帮助信息");
        System.out.println("  quit/exit      - 退出应用程序");
        System.out.println("\n注意:");
        System.out.println("  - 发送的消息会被ConfigurableNatsSubscriber接收并显示");
        System.out.println("  - 确保NATS服务器正在运行并且配置正确");
    }
}
