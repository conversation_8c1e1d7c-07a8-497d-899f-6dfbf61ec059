package vip.cjjc.chj.analyze.common.service.nats.annotation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.nats.client.Connection;
import io.nats.client.Dispatcher;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * =================================================================
 * 3. 注解处理器 (Annotation Processor)
 * 这是实现注解功能的魔法所在。它是一个 BeanPostProcessor，
 * 会在 Spring 容器初始化每个 Bean 之后，扫描其上的 @NatsListener 注解，
 * 并动态地为它们创建 NATS 订阅。
 * =================================================================
 */
@Component
class NatsListenerAnnotationProcessor implements BeanPostProcessor {

    private static final Logger log = LoggerFactory.getLogger(NatsListenerAnnotationProcessor.class);

    // 从 Spring 容器中注入 NATS 连接
    @Autowired
    private Connection natsConnection;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 扫描当前 Bean 的所有方法
        for (Method method : bean.getClass().getMethods()) {
            // 检查方法上是否有 @NatsListener 注解
            NatsListener natsListener = method.getAnnotation(NatsListener.class);
            if (natsListener != null) {
                log.info("发现 NATS 监听器: {}.{} on subject '{}'", bean.getClass().getSimpleName(), method.getName(), natsListener.subject());
                // 为找到的监听器方法创建订阅
                createSubscription(bean, method, natsListener);
            }
        }
        return bean;
    }

    private void createSubscription(Object bean, Method method, NatsListener listenerAnnotation) {
        // 创建一个 Dispatcher 来异步处理消息
        // 为每个监听器创建一个独立的 Dispatcher 可以隔离处理逻辑
        Dispatcher dispatcher = natsConnection.createDispatcher(msg -> {
            try {
                // 当消息到达时，通过反射调用被注解的原始方法
                // 这里我们将原始的 io.nats.client.Message 对象作为参数传递
                // 这样用户可以访问消息的所有信息（如 headers, replyTo 等）
                log.debug("分发消息到 {}.{}", bean.getClass().getSimpleName(), method.getName());
                method.invoke(bean, msg);
            } catch (Exception e) {
                log.error("调用 NATS 监听器方法 {} 时发生错误", method.getName(), e);
                // 在这里可以加入更复杂的错误处理逻辑，例如发送到一个死信队列
            }
        });

        // 根据注解中是否定义了 queueGroup 来决定订阅方式
        if (listenerAnnotation.queueGroup().isEmpty()) {
            dispatcher.subscribe(listenerAnnotation.subject());
        } else {
            dispatcher.subscribe(listenerAnnotation.subject(), listenerAnnotation.queueGroup());
        }

        log.info("成功为主题 '{}' 创建订阅，由方法 {}.{} 处理",
                listenerAnnotation.subject(),
                bean.getClass().getSimpleName(),
                method.getName());
    }
}