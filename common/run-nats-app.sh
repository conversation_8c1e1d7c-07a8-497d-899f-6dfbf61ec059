#!/bin/bash
# NATS服务应用启动脚本 (Linux/macOS)
# 
# 使用方法:
#   1. 确保NATS服务器正在运行
#   2. chmod +x run-nats-app.sh
#   3. ./run-nats-app.sh

echo "============================================================"
echo "    NATS 收集器代码更新服务启动脚本"
echo "============================================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 17或更高版本"
    exit 1
fi

echo "正在启动NATS服务应用..."
echo

# 设置环境变量 (可选)
# export NATS_SERVER_URL=nats://********:5222
# export CUSTOMER_TAG=zdl
# export NATS_PASSWORD=123456

# 运行应用
./gradlew test --tests "vip.cjjc.chj.analyze.common.service.nats.impl.NatsServiceApplication" --console=plain

echo
echo "应用已退出"
