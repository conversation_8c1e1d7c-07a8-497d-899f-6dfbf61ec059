@echo off
REM NATS服务应用启动脚本 (Windows)
REM 
REM 使用方法:
REM   1. 确保NATS服务器正在运行
REM   2. 双击此文件或在命令行中运行
REM   3. 按照提示进行操作

echo ============================================================
echo    NATS 收集器代码更新服务启动脚本
echo ============================================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

echo 正在启动NATS服务应用...
echo.

REM 设置环境变量 (可选)
REM set NATS_SERVER_URL=nats://********:5222
REM set CUSTOMER_TAG=zdl
REM set NATS_PASSWORD=123456

REM 运行应用
gradlew.bat test --tests "vip.cjjc.chj.analyze.common.service.nats.impl.NatsServiceApplication" --console=plain

echo.
echo 应用已退出
pause
